import Upload from "/@/views/bpts/setting/components/upload.vue";

export default {
	openItemSearch: {
		//查询区域
	},
	openItemButtons: {
		//非按钮
		createOpenItem: 'New Open Item',
		editOpenItem: 'Edit Open Item',
		viewComment: 'View Comment',
		saveOpenItem: 'Save',
		cancelOpenItem: 'Cancel',
	},
	openItemLabels: {
		TicketNumber: 'Ticket Number',
		Priority: 'Priority',
		Status: 'Status',
		Description: 'Description',
		OpenItem: 'OpenItem',
	},
	openItemFields: {
		//table 列名
		Serial: 'Item#',
		TicketId: 'TicketId',
		TicketNumber: 'Ticket#',
		Priority: 'Priority',
		Project: 'Project',
		TaskCategory: 'Task Category',
		Attachment: 'Attachment',
		Comment: 'Comment',
		Status: 'Status',
		OpenDate: 'Open Date',
		CloseDate: 'Close Date',
		LOE: 'LOE',
		ApprovedDate: 'Approved Date',
		PointOfContact: 'Point of Contact',
		Update: 'Update',
		SortId: 'SortId',
		RelatedTickets: 'Related Tickets',
		Description: 'Description',
		Created_By: 'Created By',
		Created_At: 'Created At',
		Modified_By: 'Modified By',
		Modified_At: 'Modified At',
	},
	openItemDlgTips: {
		deleteErrorTips: 'There is already a configuration relationship, deletion is not allowed!',
		addDetailTips: 'Would you like to create a ticket for this open item? ',
		detailTooltip: 'double click at a line to edit.',
		dlgStatusCompleteText: 'Are you sure you want to update the status to Complete?',
		dlgStatusCancelledText: 'Are you sure you want to update the status to Cancelled?',
		deleteOpenItemTipText: 'Are you sure you want to delete this open item and cancel the associated ticket?',
		deleteOpenItemTipTextComplete: 'It\'s not allowed to delete the open item marked in Complete',
		PleaseUpdateComment: 'Please update the comment',
	},
	openItemFilters: {
		confirmFilter: 'Filter',
		resetFilter: 'Clear',
		allFilter: 'Select All',
	}
};
